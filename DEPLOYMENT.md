# PersonPicker Deployment Guide

This guide explains how to deploy the PersonPicker website and multiplayer server using GitHub Actions.

## Prerequisites

1. A server with SSH access (Ubuntu/Debian recommended)
2. Domain name pointing to your server
3. GitHub repository with the PersonPicker code

## Required GitHub Secrets

Before deploying, you need to configure the following secrets in your GitHub repository:

### Go to: Repository Settings → Secrets and variables → Actions → New repository secret

1. **SERVER_SSH_KEY**: Your private SSH key for server access
   ```bash
   # Generate a new SSH key pair (if you don't have one)
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

   # Copy the private key content (this goes in the secret)
   cat ~/.ssh/id_rsa

   # Copy the public key to your server
   ssh-copy-id user@your-server-ip
   ```

2. **KNOWN_HOSTS**: Your server's SSH fingerprint
   ```bash
   # Get the known hosts entry for your server
   ssh-keyscan -H your-server-ip
   ```

3. **SERVER_IP**: Your server's IP address
   ```
   Example: *************
   ```

4. **SERVER_USER**: SSH username for your server
   ```
   Example: ubuntu, root, or your custom user
   ```

5. **DEPLOY_DIR**: Directory path where the website will be deployed
   ```
   Example: /var/www/personpicker.com
   ```

## Server Setup

### 1. Prepare the Server

```bash
# Update the system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx curl build-essential

# Create deployment directory
sudo mkdir -p /var/www/personpicker.com
sudo chown $USER:$USER /var/www/personpicker.com
```

### 2. Configure Nginx

Create `/etc/nginx/sites-available/personpicker.com`:

```nginx
server {
    listen 80;
    server_name personpicker.com www.personpicker.com;
    root /var/www/personpicker.com;
    index index.html;

    # Serve static files first, fallback to proxy for dynamic content
    location / {
        try_files $uri $uri/ @proxy;
    }

    # Fallback to proxy for dynamic content and WebSocket connections
    location @proxy {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/personpicker.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. SSL Certificate (Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d personpicker.com -d www.personpicker.com
```

## Deployment Process

### Automatic Deployment

The deployment happens automatically when you:
1. Push to the `main` branch
2. Manually trigger the workflow from GitHub Actions

### Manual Deployment

You can also trigger deployment manually:
1. Go to your GitHub repository
2. Click "Actions" tab
3. Select "Deploy to PersonPicker Website"
4. Click "Run workflow"

## What the Deployment Does

1. **Deploy Website Files**: Syncs all static files to the server
2. **Setup Node.js**: Installs Node.js 18.x if not present
3. **Install PM2**: Installs PM2 process manager globally
4. **Install Dependencies**: Runs `npm ci` for the multiplayer server
5. **Start Server**: Starts the WebSocket server with PM2
6. **Health Monitoring**: Sets up automatic health checks and restarts
7. **Verification**: Checks that everything is running correctly

## Server Architecture

- **Port 80/443**: Nginx serves static files and handles SSL
- **Port 4000**: Node.js WebSocket server (internal)
- **PM2**: Manages the Node.js process with auto-restart
- **Cron**: Runs health checks every 15 minutes

## Monitoring

### Check Server Status
```bash
# Check PM2 processes
pm2 list

# Check PM2 logs
pm2 logs personpicker

# Check server health
curl http://localhost:4000

# Check if WebSocket port is open
netstat -tuln | grep 4000
```

### Health Check Logs
```bash
tail -f /tmp/personpicker-health-check.log
```

## Troubleshooting

### Common Issues

1. **Server not starting**: Check PM2 logs with `pm2 logs personpicker`
2. **Port conflicts**: Ensure port 4000 is not used by other services
3. **Permission issues**: Ensure deploy directory has correct ownership
4. **SSL issues**: Run `sudo certbot renew --dry-run` to test renewal

### Restart Services
```bash
# Restart the Node.js server
pm2 restart personpicker

# Restart Nginx
sudo systemctl restart nginx

# Full reboot (if needed)
sudo reboot
```

## Security Considerations

1. **Firewall**: Only open necessary ports (22, 80, 443)
2. **SSH Keys**: Use SSH keys instead of passwords
3. **Updates**: Keep the server updated regularly
4. **Monitoring**: Monitor logs for suspicious activity

## Backup

Consider setting up automated backups of:
- Website files (`/var/www/personpicker.com`)
- Nginx configuration (`/etc/nginx/sites-available/`)
- SSL certificates (`/etc/letsencrypt/`)

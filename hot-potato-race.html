<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hot Potato - Person Picker</title>
    <link rel="stylesheet" href="/public/css/games/hot-potato-race.css">
    <script src="/public/js/confetti.js"></script>
</head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-CXC0589Q5T');
</script>
<body>
    <div id="game-container">
        <h1>Hot Potato Race</h1>
        <div id="description">In this game, players pass a hot potato for 10 seconds. Whoever is holding the potato when time runs out loses!</div>

        <div id="round-info">Hot Potato!</div>
        <div id="timer">Time remaining: 10s</div>

        <div id="players-container">
            <!-- Players will be added here dynamically -->
        </div>

        <div id="potato-container">
            <div id="potato">🥔</div>
        </div>

        <div id="eliminated-container">
            <h3>Eliminated Players</h3>
            <ul id="eliminated-list"></ul>
        </div>

        <div id="controls">
            <button id="start-btn">Start Game</button>
            <button id="reset-btn" style="display:none;">Reset Game</button>
        </div>

        <div id="winner-display"></div>
    </div>

    <script src="/public/js/games/hot-potato-race.js"></script>
</body>
</html>
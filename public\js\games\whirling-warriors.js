/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

let names = window.parent.names || [];
const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff', '#ff8000'];
let beyblades = [];
let battleInterval;
let arenaSize;

function createBeyblades() {
    const arena = document.getElementById('arena');
    arena.innerHTML = '';
    arenaSize = arena.offsetWidth;
    beyblades = names.map((name, index) => {
        const beyblade = document.createElement('div');
        beyblade.className = 'beyblade';
        const beybladeBody = document.createElement('div');
        beybladeBody.className = 'beyblade-body';
        beybladeBody.style.backgroundColor = colors[index % colors.length];
        const spinEffect = document.createElement('div');
        spinEffect.className = 'spin-effect';
        beybladeBody.appendChild(spinEffect);
        beyblade.appendChild(beybladeBody);
        const displayName = name.length > 8 ? name.substring(0, 7) + '.' : name;
        const nameSpan = document.createElement('span');
        nameSpan.textContent = displayName;
        beyblade.appendChild(nameSpan);
        arena.appendChild(beyblade);
        return {
            element: beyblade,
            bodyElement: beybladeBody,
            name: displayName,
            x: Math.random() * 91.2,
            y: Math.random() * 91.2,
            dx: (Math.random() - 0.5) * 2,
            dy: (Math.random() - 0.5) * 2,
            eliminated: false
        };
    });
}

function startBattle() {
    if (names.length < 2) {
        updateMessage("Please enter at least two names before starting the battle.");
        return;
    }
    createBeyblades();
    document.getElementById('startButton').disabled = true;
    updateMessage("The battle has begun!");
    document.getElementById('winnerMessage').textContent = '';
    document.getElementById('winnerMessage').classList.remove('show');
    battleInterval = setInterval(updateBattle, 50);
}

function updateBattle() {
    const arenaCenterX = 50;
    const arenaCenterY = 50;
    const arenaRadius = 50;
    const gravityStrength = 0.238;

    beyblades.forEach(beyblade => {
        if (!beyblade.eliminated) {
            const dx = arenaCenterX - beyblade.x;
            const dy = arenaCenterY - beyblade.y;
            const distanceFromCenter = Math.sqrt(dx * dx + dy * dy);
            const gravityFactor = gravityStrength / (distanceFromCenter + 1);
            beyblade.dx += dx * gravityFactor;
            beyblade.dy += dy * gravityFactor;

            beyblade.x += beyblade.dx;
            beyblade.y += beyblade.dy;

            if (distanceFromCenter > arenaRadius - 4.4) {
                const angle = Math.atan2(beyblade.y - arenaCenterY, beyblade.x - arenaCenterX);
                beyblade.x = arenaCenterX + (arenaRadius - 4.4) * Math.cos(angle);
                beyblade.y = arenaCenterY + (arenaRadius - 4.4) * Math.sin(angle);
                beyblade.dx = -beyblade.dx * 0.8 + (Math.random() - 0.5) * 0.1;
                beyblade.dy = -beyblade.dy * 0.8 + (Math.random() - 0.5) * 0.1;
            }

            beyblade.dx *= 0.99;
            beyblade.dy *= 0.99;

            beyblade.element.style.left = `${beyblade.x}%`;
            beyblade.element.style.top = `${beyblade.y}%`;

            beyblade.bodyElement.style.transform = `rotate(${Date.now() % 360}deg)`;

            beyblades.forEach(otherBeyblade => {
                if (beyblade !== otherBeyblade && !otherBeyblade.eliminated) {
                    const dx = beyblade.x - otherBeyblade.x;
                    const dy = beyblade.y - otherBeyblade.y;
                    const distance = Math.sqrt(dx*dx + dy*dy);
                    if (distance < 8.8) {
                        if (Math.random() < 0.1) {
                            otherBeyblade.eliminated = true;
                            otherBeyblade.element.style.opacity = '0.3';
                        } else {
                            const angle = Math.atan2(dy, dx);
                            const speed = Math.sqrt(beyblade.dx * beyblade.dx + beyblade.dy * beyblade.dy);
                            beyblade.dx = Math.cos(angle) * speed;
                            beyblade.dy = Math.sin(angle) * speed;
                        }
                    }
                }
            });
        }
    });

    const activeBeyblades = beyblades.filter(b => !b.eliminated);
    if (activeBeyblades.length === 1) {
        clearInterval(battleInterval);
        announceWinner(activeBeyblades[0]);
    }
}

function announceWinner(winner) {
    updateMessage(`${winner.name} is the last Whirling Warrior standing!`);
    document.getElementById('startButton').disabled = false;
    
    window.parent.confetti?.({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });
    
    const winnerMessage = document.getElementById('winnerMessage');
    winnerMessage.textContent = `🎉 ${winner.name} is the champion! 🎉`;
    winnerMessage.classList.add('show');
}

function updateMessage(msg) {
    document.getElementById('message').textContent = msg;
}

// Initialize
window.addEventListener('load', () => {
    names = window.parent.names || [];
    createBeyblades();
    updateMessage("Click 'Start Battle' to begin!");
}); 
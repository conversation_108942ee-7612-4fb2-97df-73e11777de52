/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

import NamesManager from '/public/js/core/namesManager.js';
import GameLoader from '/public/js/core/gameLoader.js';

// Game data
const games = [
    { name: 'Grid Quest', icon: '🔍', file: 'grid-quest.html' },
    { name: 'Whirling Warriors', icon: '🌪️', file: 'whirling-warriors.html' },
    { name: 'Chance Chest Challenge', icon: '🧰', file: 'chance-chest-challenge.html' },
    { name: 'Prehistoric Derby', icon: '🦖', file: 'prehistoric-derby.html' },
    { name: 'Mystic Shell Oracle', icon: '🔮', file: 'shell-oracle.html' },
    { name: 'Celestial Cascade', icon: '☄️', file: 'celestial-cascade.html' },
    { name: 'Wheel of Fortune', icon: '🎡', file: 'wheel-of-fortune.html' },
    { name: 'Rock Paper Scissors Tournament', icon: '✂️', file: 'rock-paper-scissors-tournament.html' },
    { name: 'Balloon Pop Parade', icon: '🎈', file: 'balloon-pop-parade.html' },
    { name: 'Time Bomb Game', icon: '💣', file: 'time-bomb.html' },
    { name: 'Hot Potato Race', icon: '🥔', file: 'hot-potato-race.html' }
];

// DOM elements
const elements = {
    nameInputs: document.getElementById('nameInputs'),
    addNameBtn: document.getElementById('addNameBtn'),
    launchButton: document.getElementById('launchButton'),
    gameFrame: document.getElementById('gameFrame'),
    backButton: document.getElementById('backButton'),
    container: document.querySelector('.container'),
    gameIcons: document.getElementById('gameIcons')
};

// Note: Names state is now managed by NamesManager class
let namesManagerInstance;

// Note: The following functions have been moved to NamesManager class:
// - initializeApp
// - initializeNameInputs
// - addNameInput
// - updateNames

// Launch game
const launchGame = (game) => {
    const names = namesManagerInstance.getNames();
    if (names.length < 2) {
        alert('Please enter at least two names before launching a game.');
        return;
    }
    elements.gameFrame.src = game.file;
    toggleGameVisibility(true);
    announceGameLaunch(game.name);
};

// Toggle game visibility
const toggleGameVisibility = (showGame) => {
    elements.container.style.display = showGame ? 'none' : 'block';
    elements.gameFrame.style.display = showGame ? 'block' : 'none';
    elements.backButton.style.display = showGame ? 'block' : 'none';
};

// Announce game launch for screen readers
const announceGameLaunch = (gameName) => {
    const messageArea = document.createElement('div');
    messageArea.setAttribute('aria-live', 'polite');
    messageArea.className = 'sr-only';
    messageArea.textContent = `Launched ${gameName}`;
    document.body.appendChild(messageArea);
    setTimeout(() => messageArea.remove(), 3000);
};

// Event listeners
// Note: addNameBtn event listener is handled by NamesManager class

elements.backButton.addEventListener('click', () => {
    toggleGameVisibility(false);
    elements.gameFrame.src = '';
});

elements.launchButton.addEventListener('click', () => {
    const names = namesManagerInstance.getNames();
    if (names.length < 2) {
        alert('Please enter at least two names before launching a game.');
        return;
    }
    const randomGame = games[Math.floor(Math.random() * games.length)];
    launchGame(randomGame);
});

elements.gameFrame.addEventListener('error', () => {
    alert('There was an error loading the game. Please try again.');
    toggleGameVisibility(false);
});

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    namesManagerInstance = new NamesManager();
    namesManagerInstance.initializeNameInputs(); // Initialize name inputs through NamesManager
    const gameLoader = new GameLoader(namesManagerInstance);
    // No need to call initializeApp() as NamesManager handles name inputs
});
/**
 * Person Picker - A random name picker with fun games
 * Copyright (C) 2024 PersonPicker.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

// DOM Elements
const startBtn = document.getElementById('start-btn');
const resetBtn = document.getElementById('reset-btn');
const playersContainer = document.getElementById('players-container');
const eliminatedList = document.getElementById('eliminated-list');
const roundInfo = document.getElementById('round-info');
const timer = document.getElementById('timer');
const potato = document.getElementById('potato');
const winnerDisplay = document.getElementById('winner-display');

// Game variables
let players = [];
let activePlayers = [];
let eliminatedPlayers = [];
let currentRound = 1;
let currentPlayerIndex = 0;
let gameActive = false;
let roundTimeout;
let passInterval;
let timerInterval;
let timeRemaining;
let roundDuration = 10000; // 10 seconds per round
let passRate = 800; // Starting pass speed in ms (will decrease each round)

/**
 * Initializes the game with player names
 */
function startGame() {
    // Get player names from localStorage or parent window
    let storedNames;

    // Try to get names from window.names (set by parent frame) first
    if (window.names && window.names.length > 0) {
        storedNames = window.names;
    } else {
        // Fallback to localStorage if window.names is not available
        storedNames = JSON.parse(localStorage.getItem('names') || '[]');
    }

    if (!storedNames || storedNames.length < 2) {
        alert('Please enter at least two names to play the game.');
        return;
    }

    // Initialize with the stored names
    players = [...storedNames];
    activePlayers = [...players];
    eliminatedPlayers = [];

    // Render players
    renderPlayers();

    // Hide start button, show reset button
    startBtn.style.display = 'none';
    resetBtn.style.display = 'inline-block';

    // Start first round
    startRound();
}

/**
 * Renders player elements in the container
 */
function renderPlayers() {
    playersContainer.innerHTML = '';

    activePlayers.forEach((name, index) => {
        const playerElement = document.createElement('div');
        playerElement.className = 'player';
        playerElement.setAttribute('data-index', index);
        playerElement.textContent = name;
        playersContainer.appendChild(playerElement);
    });
}

/**
 * Starts a new round
 */
function startRound() {
    // Reset game state for the round
    gameActive = true;
    currentPlayerIndex = Math.floor(Math.random() * activePlayers.length);
    roundInfo.textContent = `Hot Potato!`;

    // Set pass rate
    passRate = 800;

    // Set the round timer
    timeRemaining = roundDuration;
    updateTimer();

    // Add hot potato animation
    potato.classList.add('hot');

    // Reset potato position to center
    potato.style.transition = 'none';
    potato.style.left = '50%';
    potato.style.top = '0';
    potato.style.transform = 'translateX(-50%)';

    // Mark initial player as active
    updateActivePlayer();

    // Start passing the potato
    passInterval = setInterval(passHotPotato, passRate);

    // Start countdown timer
    timerInterval = setInterval(() => {
        timeRemaining -= 100;
        updateTimer();

        // Add warning class when time is running low
        if (timeRemaining <= 3000) {
            timer.classList.add('warning');
        }

        if (timeRemaining <= 0) {
            endRound();
        }
    }, 100);

    // Set maximum round duration
    roundTimeout = setTimeout(endRound, roundDuration);
}

/**
 * Updates the timer display
 */
function updateTimer() {
    const seconds = (timeRemaining / 1000).toFixed(1);
    timer.textContent = `Time remaining: ${seconds}s`;
}

/**
 * Passes the hot potato to the next player
 */
function passHotPotato() {
    // Get current and next player elements
    const playerElements = document.querySelectorAll('.player');
    const currentPlayer = playerElements[currentPlayerIndex];

    // Calculate next player index
    const nextPlayerIndex = (currentPlayerIndex + 1) % activePlayers.length;
    const nextPlayer = playerElements[nextPlayerIndex];

    // Get positions for animation
    if (currentPlayer && nextPlayer) {
        const currentRect = currentPlayer.getBoundingClientRect();
        const nextRect = nextPlayer.getBoundingClientRect();
        const potatoContainer = document.getElementById('potato-container');
        const containerRect = potatoContainer.getBoundingClientRect();

        // Calculate start and end positions relative to the potato container
        const startX = currentRect.left + (currentRect.width / 2) - containerRect.left - 24;
        const startY = currentRect.top - containerRect.top - 60;
        const endX = nextRect.left + (nextRect.width / 2) - containerRect.left - 24;
        const endY = nextRect.top - containerRect.top - 60;

        // Position potato at current player
        potato.style.transition = 'none';
        potato.style.left = startX + 'px';
        potato.style.top = startY + 'px';
        potato.style.transform = 'none';

        // Force reflow
        void potato.offsetWidth;

        // Animate to next player
        potato.style.transition = 'all 0.4s ease-in-out';
        potato.style.left = endX + 'px';
        potato.style.top = endY + 'px';
    }

    // Remove active class from current player
    playerElements.forEach(el => el.classList.remove('active'));

    // Move to next player
    currentPlayerIndex = nextPlayerIndex;

    // Update active player
    updateActivePlayer();
}

/**
 * Updates the active player display
 */
function updateActivePlayer() {
    const playerElements = document.querySelectorAll('.player');
    playerElements.forEach(el => {
        if (parseInt(el.getAttribute('data-index')) === currentPlayerIndex) {
            el.classList.add('active');
        } else {
            el.classList.remove('active');
        }
    });
}

/**
 * Ends the current round
 */
function endRound() {
    // Clear all intervals and timeouts
    clearInterval(passInterval);
    clearInterval(timerInterval);
    clearTimeout(roundTimeout);

    // Stop hot potato animation
    potato.classList.remove('hot');
    timer.classList.remove('warning');

    // Get the player holding the potato (the loser)
    const loser = activePlayers[currentPlayerIndex];

    // Add to eliminated list
    const listItem = document.createElement('li');
    listItem.textContent = `${loser} got caught with the hot potato!`;
    eliminatedList.appendChild(listItem);

    // End the game immediately
    endGame(loser);
}

/**
 * Ends the game and displays the result
 */
function endGame(loser) {
    gameActive = false;

    // Display loser
    winnerDisplay.textContent = `💥 ${loser} got caught with the hot potato! 💥`;
    winnerDisplay.classList.add('show');

    // Highlight the player who lost
    const playerElements = document.querySelectorAll('.player');
    playerElements.forEach(el => {
        if (parseInt(el.getAttribute('data-index')) === currentPlayerIndex) {
            el.classList.add('eliminated');
        }
    });

    // Trigger confetti for fun anyway
    if (typeof confetti === 'function') {
        confetti({
            particleCount: 200,
            spread: 180,
            origin: { y: 0.6 }
        });

        // Secondary confetti burst
        setTimeout(() => {
            confetti({
                particleCount: 100,
                spread: 120,
                origin: { y: 0.7 }
            });
        }, 500);
    }
}

/**
 * Resets the game to initial state
 */
function resetGame() {
    // Clear all intervals and timeouts
    clearInterval(passInterval);
    clearInterval(timerInterval);
    clearTimeout(roundTimeout);

    // Reset game state
    players = [];
    activePlayers = [];
    eliminatedPlayers = [];
    currentRound = 1;
    gameActive = false;

    // Reset DOM elements
    playersContainer.innerHTML = '';
    eliminatedList.innerHTML = '';
    roundInfo.textContent = 'Hot Potato!';
    timer.textContent = 'Time remaining: 10s';
    timer.classList.remove('warning');
    potato.classList.remove('hot', 'passing');
    potato.style.transition = 'none';
    potato.style.left = '50%';
    potato.style.top = '0';
    potato.style.transform = 'translateX(-50%)';
    winnerDisplay.textContent = '';
    winnerDisplay.classList.remove('show');

    // Show start button, hide reset button
    startBtn.style.display = 'inline-block';
    resetBtn.style.display = 'none';
}

// Event listeners
startBtn.addEventListener('click', startGame);
resetBtn.addEventListener('click', resetGame);

// Allow start function to be called from parent window
window.startGame = startGame;
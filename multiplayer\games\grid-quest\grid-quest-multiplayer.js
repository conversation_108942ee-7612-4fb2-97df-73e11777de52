/*!
 * Person Picker Multiplayer Grid Quest (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

// Game state
let gameState = {
    players: [],
    positions: {},
    currentPlayerIndex: 0,
    guessCount: 0,
    gameStarted: false,
    gameEnded: false,
    winner: null
};

// Client state
let clientState = {
    clientId: null,
    playerName: '',
    selectedEmoji: '👤', // Default emoji
    roomId: null,
    isHost: false,
    isMyTurn: false
};

// Available emojis for selection
const availableEmojis = [
    '😀', '😎', '🤠', '👻', '🐱', '🐶', '🦊', '🐼',
    '🐨', '🦁', '🐯', '🦄', '🐙', '🦋', '🌟'
];

// DOM Elements
const elements = {
    // Sections
    lobbySection: document.getElementById('lobbySection'),
    roomSection: document.getElementById('roomSection'),
    gameSection: document.getElementById('gameSection'),

    // <PERSON>bby controls
    playerNameInput: document.getElementById('playerName'),
    emojiSelector: document.getElementById('emojiSelector'),
    joinEmojiSelector: document.getElementById('joinEmojiSelector'),
    createRoomBtn: document.getElementById('createRoomBtn'),
    joinRoomBtn: document.getElementById('joinRoomBtn'),
    joinRoomForm: document.getElementById('joinRoomForm'),
    roomIdInput: document.getElementById('roomIdInput'),
    confirmJoinBtn: document.getElementById('confirmJoinBtn'),
    cancelJoinBtn: document.getElementById('cancelJoinBtn'),

    // Room controls
    roomIdDisplay: document.getElementById('roomId'),
    copyLinkBtn: document.getElementById('copyLink'),
    playerList: document.getElementById('playerList'),
    hostControls: document.getElementById('hostControls'),
    startGameBtn: document.getElementById('startGameBtn'),
    leaveRoomBtn: document.getElementById('leaveRoomBtn'),

    // Game elements
    turnIndicator: document.getElementById('turnIndicator'),
    teamList: document.getElementById('teamList'),
    grid: document.getElementById('grid'),
    message: document.getElementById('message'),
    newGameBtn: document.getElementById('newGameBtn'),

    // Status
    connectionStatus: document.getElementById('connectionStatus'),
    statusMessage: document.getElementById('statusMessage')
};

// Initialize multiplayer client
const client = new MultiplayerClient({
    debug: true
});

// Initialize the application
function init() {
    // Add event listeners for UI elements after DOM is ready
    if (elements.createRoomBtn) elements.createRoomBtn.addEventListener('click', createRoom);
    if (elements.joinRoomBtn) elements.joinRoomBtn.addEventListener('click', showJoinRoomForm);
    if (elements.confirmJoinBtn) elements.confirmJoinBtn.addEventListener('click', joinRoom);
    if (elements.cancelJoinBtn) elements.cancelJoinBtn.addEventListener('click', hideJoinRoomForm);
    if (elements.copyLinkBtn) elements.copyLinkBtn.addEventListener('click', copyRoomLink);
    if (elements.startGameBtn) elements.startGameBtn.addEventListener('click', startGame);
    if (elements.leaveRoomBtn) elements.leaveRoomBtn.addEventListener('click', leaveRoom);
    if (elements.newGameBtn) elements.newGameBtn.addEventListener('click', requestNewGame);

    // Try to load player name from localStorage
    const savedName = localStorage.getItem('playerName');
    if (savedName && elements.playerNameInput) {
        elements.playerNameInput.value = savedName;
    }

    // Try to load selected emoji from localStorage
    const savedEmoji = localStorage.getItem('selectedEmoji');
    if (savedEmoji) {
        clientState.selectedEmoji = savedEmoji;
    }

    // Initialize emoji selector
    initEmojiSelector();

    // Connect to the WebSocket server
    connectToServer();
}

// Initialize the emoji selector
function initEmojiSelector() {
    // Initialize main emoji selector
    initSingleEmojiSelector(elements.emojiSelector);

    // Initialize join form emoji selector
    initSingleEmojiSelector(elements.joinEmojiSelector);
}

// Initialize a single emoji selector
function initSingleEmojiSelector(selectorElement) {
    // Clear existing content
    selectorElement.innerHTML = '';

    // Add emoji options
    availableEmojis.forEach(emoji => {
        const emojiElement = document.createElement('div');
        emojiElement.className = 'emoji-option';
        if (emoji === clientState.selectedEmoji) {
            emojiElement.classList.add('selected');
        }
        emojiElement.textContent = emoji;
        emojiElement.addEventListener('click', () => selectEmoji(emoji));
        selectorElement.appendChild(emojiElement);
    });
}

// Handle emoji selection
function selectEmoji(emoji) {
    // Update selected emoji
    clientState.selectedEmoji = emoji;

    // Save to localStorage
    localStorage.setItem('selectedEmoji', emoji);

    // Update both selectors
    updateEmojiSelectorUI(elements.emojiSelector, emoji);
    updateEmojiSelectorUI(elements.joinEmojiSelector, emoji);
}

// Update the UI of an emoji selector
function updateEmojiSelectorUI(selectorElement, selectedEmoji) {
    if (!selectorElement) return;

    const options = selectorElement.querySelectorAll('.emoji-option');
    options.forEach(el => {
        el.classList.remove('selected');
        if (el.textContent === selectedEmoji) {
            el.classList.add('selected');
        }
    });
}

// Connect to the WebSocket server
function connectToServer() {
    updateConnectionStatus('connecting');

    client.connect()
        .then(() => {
            updateConnectionStatus('connected');
        })
        .catch(error => {
            console.error('Failed to connect:', error);
            updateConnectionStatus('disconnected');
            showStatusMessage('Failed to connect to server. Please try again.', 'error');
        });
}

// Create a new game room
function createRoom() {
    const playerName = elements.playerNameInput.value.trim();
    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Create room with selected emoji
    client.createRoom('grid-quest', playerName, clientState.selectedEmoji);
}

// Show the join room form
function showJoinRoomForm() {
    elements.joinRoomForm.classList.remove('hidden');

    // Make sure the emoji selector is initialized with the current selection
    updateEmojiSelectorUI(elements.joinEmojiSelector, clientState.selectedEmoji);
}

// Hide the join room form
function hideJoinRoomForm() {
    elements.joinRoomForm.classList.add('hidden');
}

// Join an existing room
function joinRoom() {
    const playerName = elements.playerNameInput.value.trim();
    const roomId = elements.roomIdInput.value.trim();

    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    if (!roomId) {
        showStatusMessage('Please enter a room ID', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Join room with selected emoji
    client.joinRoom(roomId, playerName, clientState.selectedEmoji);
}

// Copy room link to clipboard
function copyRoomLink() {
    const roomId = clientState.roomId;
    const url = `${window.location.origin}${window.location.pathname}?roomId=${roomId}`;

    navigator.clipboard.writeText(url)
        .then(() => {
            showStatusMessage('Room link copied to clipboard', 'success');
        })
        .catch(err => {
            console.error('Failed to copy:', err);
            showStatusMessage('Failed to copy link', 'error');
        });
}

// Leave the current room
function leaveRoom() {
    client.leaveRoom();
    showLobby();
}

// Start the game (host only)
function startGame() {
    if (!clientState.isHost) return;

    const players = client.room.players.map(p => p.name);
    if (players.length < 2) {
        showStatusMessage('Need at least 2 players to start', 'error');
        return;
    }

    // Initialize game state
    const gridSize = 6;
    const cells = gridSize * gridSize;
    const shuffled = [...Array(cells).keys()].sort(() => 0.5 - Math.random());

    const positions = {};
    players.forEach((player, index) => {
        positions[player] = shuffled[index];
    });

    const initialState = {
        players: players,
        positions: positions,
        currentPlayerIndex: 0,
        guessCount: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null
    };

    // Start the game
    client.startGame(initialState);
}

// Request a new game after the current one ends
function requestNewGame() {
    if (!clientState.isHost) return;

    // Reset game state
    const players = client.room.players.map(p => p.name);
    const gridSize = 6;
    const cells = gridSize * gridSize;
    const shuffled = [...Array(cells).keys()].sort(() => 0.5 - Math.random());

    const positions = {};
    players.forEach((player, index) => {
        positions[player] = shuffled[index];
    });

    const newGameState = {
        players: players,
        positions: positions,
        currentPlayerIndex: 0,
        guessCount: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null
    };

    // Send game action to start a new game
    client.sendGameAction('new_game', newGameState);
}

// Make a guess on the grid
function makeGuess(index) {
    if (!gameState.gameStarted || gameState.gameEnded || !clientState.isMyTurn) return;

    const currentPlayer = gameState.players[gameState.currentPlayerIndex];

    // Send the guess action
    client.sendGameAction('make_guess', {
        playerName: clientState.playerName,
        guessIndex: index,
        currentPlayer: currentPlayer
    });
}

// Process a guess and update game state
function processGuess(playerName, index) {
    if (!gameState.gameStarted || gameState.gameEnded) return;

    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    gameState.guessCount++;

    const cells = document.getElementsByClassName('cell');
    const cellIndex = index + 8 + Math.floor(index / 6);

    if (gameState.positions[currentPlayer] === index) {
        // Player found themselves
        cells[cellIndex].classList.add('hit');
        cells[cellIndex].textContent = currentPlayer.substring(0, 3);
        updateMessage(`${currentPlayer} has found themselves and have been selected!`);
        endGame(currentPlayer);
    } else {
        const hitPlayer = Object.keys(gameState.positions).find(player => gameState.positions[player] === index);
        if (hitPlayer) {
            // Player found someone else
            cells[cellIndex].classList.add('hit');
            cells[cellIndex].textContent = hitPlayer.substring(0, 3);
            updateMessage(`${currentPlayer} found ${hitPlayer}! ${hitPlayer} is the winner!`);
            endGame(hitPlayer);
        } else {
            // Miss
            cells[cellIndex].classList.add('miss');
            cells[cellIndex].textContent = '×';
            nextTurn();
        }
    }

    // Update game state
    client.sendGameAction('update_state', gameState);
}

// Move to the next player's turn
function nextTurn() {
    gameState.currentPlayerIndex = (gameState.currentPlayerIndex + 1) % gameState.players.length;
    const nextPlayer = gameState.players[gameState.currentPlayerIndex];
    updateMessage(`${nextPlayer}, it's your turn to guess!`);
    updateTurnIndicator();
}

// End the game with a winner
function endGame(winner) {
    gameState.gameEnded = true;
    gameState.winner = winner;

    const cells = document.getElementsByClassName('cell');
    for (let i = 0; i < cells.length; i++) {
        if (cells[i].onclick) {
            cells[i].onclick = null;
            const reference = cells[i].getAttribute('data-reference');
            if (reference) {
                const row = parseInt(reference.substring(1)) - 1;
                const col = reference.charCodeAt(0) - 65;
                const index = row * 6 + col;
                const player = Object.keys(gameState.positions).find(p => gameState.positions[p] === index);
                if (player && !cells[i].classList.contains('hit')) {
                    cells[i].textContent = player.substring(0, 3);
                    cells[i].classList.add('hit');
                    if (player === winner) {
                        cells[i].classList.add('winner-highlight');
                    }
                }
            }
        }
    }

    // Add celebrate class to the message element
    elements.message.classList.add('celebrate');

    // Create confetti
    confetti?.({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });

    // Highlight the winner in the team list
    const teamMembers = document.getElementsByClassName('team-member');
    for (let member of teamMembers) {
        if (member.textContent === winner) {
            member.classList.add('winner-highlight');
        }
    }

    // Show new game button for host
    if (clientState.isHost) {
        elements.newGameBtn.classList.remove('hidden');
    }
}

// Create the game grid
function createGrid() {
    elements.grid.innerHTML = '';
    const letters = ['', 'A', 'B', 'C', 'D', 'E', 'F'];

    for (let i = 0; i < 7; i++) {
        for (let j = 0; j < 7; j++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            if (i === 0 && j === 0) {
                cell.textContent = '';
            } else if (i === 0) {
                cell.textContent = letters[j];
                cell.classList.add('reference');
            } else if (j === 0) {
                cell.textContent = i;
                cell.classList.add('reference');
            } else {
                const index = (i - 1) * 6 + (j - 1);
                cell.onclick = () => makeGuess(index);
                cell.setAttribute('data-reference', `${letters[j]}${i}`);
            }
            elements.grid.appendChild(cell);
        }
    }
}

// Display team members
function displayTeamMembers() {
    elements.teamList.innerHTML = gameState.players.map(player =>
        `<span class="team-member">${player}</span>`
    ).join('');
}

// Update the message display
function updateMessage(msg) {
    elements.message.textContent = msg;
}

// Update the turn indicator
function updateTurnIndicator() {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    clientState.isMyTurn = currentPlayer === clientState.playerName;

    if (gameState.gameEnded) {
        elements.turnIndicator.textContent = `Game over! ${gameState.winner} wins!`;
    } else if (gameState.gameStarted) {
        elements.turnIndicator.textContent = `Current turn: ${currentPlayer}`;
        if (clientState.isMyTurn) {
            elements.turnIndicator.textContent += ' (Your turn!)';
        }
    } else {
        elements.turnIndicator.textContent = 'Waiting for game to start...';
    }
}

// Update the player list
function updatePlayerList() {
    if (!client.room) return;

    const players = client.room.players;
    elements.playerList.innerHTML = '';

    players.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';

        // Create player info container (emoji + name)
        const playerInfo = document.createElement('div');
        playerInfo.className = 'player-info';

        // Add emoji
        const emojiSpan = document.createElement('span');
        emojiSpan.className = 'player-emoji';
        emojiSpan.textContent = player.emoji || '👤'; // Use default if not set
        playerInfo.appendChild(emojiSpan);

        // Add name
        const nameSpan = document.createElement('span');
        nameSpan.className = 'player-name';
        nameSpan.textContent = player.name;

        if (player.id === clientState.clientId) {
            nameSpan.textContent += ' (You)';
        }

        playerInfo.appendChild(nameSpan);

        // Add status (host)
        const statusSpan = document.createElement('span');
        if (player.isHost) {
            statusSpan.className = 'player-host';
            statusSpan.textContent = 'Host';
        }

        playerItem.appendChild(playerInfo);
        playerItem.appendChild(statusSpan);
        elements.playerList.appendChild(playerItem);
    });
}

// Show the lobby section
function showLobby() {
    elements.lobbySection.classList.remove('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the room section
function showRoom() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.remove('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the game section
function showGame() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.remove('hidden');
}

// Update connection status display
function updateConnectionStatus(status) {
    elements.connectionStatus.className = 'connection-status';

    switch (status) {
        case 'connected':
            elements.connectionStatus.classList.add('connection-connected');
            elements.connectionStatus.textContent = 'Connected';
            break;
        case 'disconnected':
            elements.connectionStatus.classList.add('connection-disconnected');
            elements.connectionStatus.textContent = 'Disconnected';
            break;
        case 'connecting':
            elements.connectionStatus.classList.add('connection-connecting');
            elements.connectionStatus.textContent = 'Connecting...';
            break;
    }
}

// Show a status message
function showStatusMessage(message, type = 'info') {
    elements.statusMessage.textContent = message;
    elements.statusMessage.className = 'status-message fade-in';
    elements.statusMessage.classList.add(`status-${type}`);
    elements.statusMessage.classList.remove('hidden');

    setTimeout(() => {
        elements.statusMessage.classList.add('hidden');
    }, 3000);
}

// Event handlers for WebSocket events
client.on('connected', (data) => {
    clientState.clientId = data.clientId;

    // Check for pending join from lobby browser
    const pendingJoin = sessionStorage.getItem('pendingJoin');
    if (pendingJoin) {
        try {
            const joinData = JSON.parse(pendingJoin);
            // Clear the pending join data
            sessionStorage.removeItem('pendingJoin');

            // Set player name and join the room automatically
            elements.playerNameInput.value = joinData.playerName;
            clientState.playerName = joinData.playerName;
            localStorage.setItem('playerName', joinData.playerName);

            // Join the room directly
            client.joinRoom(joinData.roomId, joinData.playerName, clientState.selectedEmoji);
            return;
        } catch (error) {
            console.error('Error processing pending join:', error);
            sessionStorage.removeItem('pendingJoin');
        }
    }

    // Check if there's a room ID in the URL (fallback for direct links)
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = urlParams.get('roomId') || urlParams.get('join');
    if (roomId) {
        elements.roomIdInput.value = roomId;
        showJoinRoomForm();
    }
});

client.on('room_created', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = true;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.remove('hidden');
    updatePlayerList();
    showRoom();
});

client.on('room_joined', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = false;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.add('hidden');
    updatePlayerList();
    showRoom();
});

client.on('player_joined', (data) => {
    updatePlayerList();
    showStatusMessage(`${data.player.name} joined the room`, 'info');
});

client.on('player_left', (data) => {
    updatePlayerList();
    showStatusMessage(`A player left the room`, 'warning');
});

client.on('game_started', (data) => {
    gameState = data.room.gameState;
    createGrid();
    displayTeamMembers();
    updateTurnIndicator();
    updateMessage(`${gameState.players[0]}, it's your turn to guess!`);
    elements.newGameBtn.classList.add('hidden');
    showGame();
});

client.on('game_action', (data) => {
    if (data.action === 'make_guess') {
        processGuess(data.playerId, data.gameState.guessIndex);
    } else if (data.action === 'update_state') {
        gameState = data.gameState;
        updateTurnIndicator();
    } else if (data.action === 'new_game') {
        gameState = data.gameState;
        createGrid();
        displayTeamMembers();
        updateTurnIndicator();
        updateMessage(`${gameState.players[0]}, it's your turn to guess!`);
        elements.message.classList.remove('celebrate');
        elements.newGameBtn.classList.add('hidden');
    }
});

client.on('error', (data) => {
    showStatusMessage(data.message, 'error');
});

client.on('disconnected', () => {
    updateConnectionStatus('disconnected');
    showStatusMessage('Disconnected from server. Trying to reconnect...', 'error');
});

client.on('reconnected', () => {
    updateConnectionStatus('connected');
    showStatusMessage('Reconnected to server', 'success');
});

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

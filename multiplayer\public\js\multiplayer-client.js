/*!
 * Person Picker Multiplayer Client (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

class MultiplayerClient {
    constructor(options = {}) {
        // Determine WebSocket URL based on current location
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsHost = window.location.hostname;
        const defaultUrl = `${wsProtocol}//${wsHost}:8080`;

        this.options = {
            serverUrl: options.serverUrl || defaultUrl,
            reconnectAttempts: options.reconnectAttempts || 5,
            reconnectDelay: options.reconnectDelay || 3000,
            debug: options.debug || false
        };

        this.socket = null;
        this.clientId = null;
        this.roomId = null;
        this.playerName = null;
        this.isHost = false;
        this.room = null;
        this.reconnectCount = 0;
        this.eventListeners = {};

        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.reconnect = this.reconnect.bind(this);
        this.handleMessage = this.handleMessage.bind(this);
        this.handleClose = this.handleClose.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * Connect to the WebSocket server
     * @returns {Promise} Resolves when connected
     */
    connect() {
        return new Promise((resolve, reject) => {
            try {
                this.socket = new WebSocket(this.options.serverUrl);

                this.socket.onopen = () => {
                    this.log('Connected to server');
                    this.reconnectCount = 0;
                    resolve();
                };

                this.socket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };

                this.socket.onclose = this.handleClose;
                this.socket.onerror = (error) => {
                    this.handleError(error);
                    reject(error);
                };
            } catch (error) {
                this.log('Connection error:', error);
                reject(error);
            }
        });
    }

    /**
     * Disconnect from the WebSocket server
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }

    /**
     * Attempt to reconnect to the server
     * @private
     */
    reconnect() {
        if (this.reconnectCount >= this.options.reconnectAttempts) {
            this.log('Max reconnect attempts reached');
            this.emit('max_reconnect_attempts');
            return;
        }

        this.reconnectCount++;
        this.log(`Reconnecting (${this.reconnectCount}/${this.options.reconnectAttempts})...`);

        setTimeout(() => {
            this.connect()
                .then(() => {
                    this.emit('reconnected');

                    // Rejoin room if we were in one
                    if (this.roomId && this.playerName) {
                        this.joinRoom(this.roomId, this.playerName, this.emoji);
                    }
                })
                .catch(() => {
                    this.reconnect();
                });
        }, this.options.reconnectDelay);
    }

    /**
     * Create a new game room
     * @param {string} game - Game type
     * @param {string} playerName - Player's name
     * @param {string} emoji - Player's emoji
     */
    createRoom(game, playerName, emoji) {
        this.playerName = playerName;
        this.emoji = emoji;
        this.isHost = true;

        this.send({
            type: 'create_room',
            game: game,
            playerName: playerName,
            emoji: emoji
        });
    }

    /**
     * Join an existing game room
     * @param {string} roomId - Room ID to join
     * @param {string} playerName - Player's name
     * @param {string} emoji - Player's emoji
     */
    joinRoom(roomId, playerName, emoji) {
        this.playerName = playerName;
        this.emoji = emoji;

        this.send({
            type: 'join_room',
            roomId: roomId,
            playerName: playerName,
            emoji: emoji
        });
    }

    /**
     * Leave the current game room
     */
    leaveRoom() {
        if (!this.roomId) return;

        this.send({
            type: 'leave_room'
        });

        this.roomId = null;
        this.isHost = false;
        this.room = null;
    }

    /**
     * Start the game (host only)
     * @param {Object} initialState - Initial game state
     */
    startGame(initialState = {}) {
        if (!this.isHost) {
            this.log('Only the host can start the game');
            return;
        }

        this.send({
            type: 'start_game',
            initialState: initialState
        });
    }

    /**
     * Send a game action
     * @param {string} action - Action type
     * @param {Object} gameState - Updated game state
     */
    sendGameAction(action, gameState = null) {
        this.send({
            type: 'game_action',
            action: action,
            gameState: gameState
        });
    }

    /**
     * Send a message to the server
     * @param {Object} message - Message to send
     * @private
     */
    send(message) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            this.log('Cannot send message, socket not connected');
            return;
        }

        this.socket.send(JSON.stringify(message));
    }

    /**
     * Handle incoming messages
     * @param {Object} data - Message data
     * @private
     */
    handleMessage(data) {
        this.log('Received message:', data);

        switch (data.type) {
            case 'connected':
                this.clientId = data.clientId;
                this.emit('connected', data);
                break;

            case 'room_created':
                this.roomId = data.roomId;
                this.room = data.room;
                this.emit('room_created', data);
                break;

            case 'room_joined':
                this.roomId = data.roomId;
                this.room = data.room;
                this.emit('room_joined', data);
                break;

            case 'player_joined':
                this.room = data.room;
                this.emit('player_joined', data);
                break;

            case 'player_left':
                this.room = data.room;
                this.emit('player_left', data);
                break;

            case 'game_started':
                this.room = data.room;
                this.emit('game_started', data);
                break;

            case 'game_action':
                this.emit('game_action', data);
                break;

            case 'error':
                this.emit('error', data);
                break;

            default:
                this.log('Unknown message type:', data.type);
        }
    }

    /**
     * Handle WebSocket close event
     * @private
     */
    handleClose() {
        this.log('Connection closed');
        this.emit('disconnected');
        this.reconnect();
    }

    /**
     * Handle WebSocket error event
     * @param {Error} error - Error object
     * @private
     */
    handleError(error) {
        this.log('WebSocket error:', error);
        this.emit('error', { message: 'WebSocket error' });
    }

    /**
     * Register an event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }

        this.eventListeners[event].push(callback);
    }

    /**
     * Remove an event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    off(event, callback) {
        if (!this.eventListeners[event]) return;

        this.eventListeners[event] = this.eventListeners[event].filter(
            cb => cb !== callback
        );
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {Object} data - Event data
     * @private
     */
    emit(event, data) {
        if (!this.eventListeners[event]) return;

        this.eventListeners[event].forEach(callback => {
            callback(data);
        });
    }

    /**
     * Log a message if debug is enabled
     * @private
     */
    log(...args) {
        if (this.options.debug) {
            console.log('[MultiplayerClient]', ...args);
        }
    }
}

// Export for both browser and Node.js environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiplayerClient;
} else {
    window.MultiplayerClient = MultiplayerClient;
}
